import os
import sys
import awswrangler as wr

# Add paths to sys.path mimicking csm_driver.sh approach - MUST be done before any other imports
# Add modelFactory folder to PYTHONPATH for imports to work (equivalent to $(dirname "$INSTALL_DIR"))
script_dir = os.path.dirname(os.path.realpath(__file__))
model_factory_dir = os.path.dirname(script_dir)
sys.path.insert(0, model_factory_dir)

# Add common python utils to PYTHONPATH (equivalent to "$INSTALL_DIR/../../common/pyUtils")
common_pyutils_dir = os.path.abspath(os.path.join(script_dir, '..', '..', 'common', 'pyUtils'))
sys.path.insert(0, common_pyutils_dir)

print(f"Added to Python path: {model_factory_dir}")
print(f"Added to Python path: {common_pyutils_dir}")

# Now import everything else
import json
import pandas as pd
import openai
from io import StringIO
import boto3
import csv


# Import existing data access layer
from content_selection.data_access_layer import DataAccessLayer
from content_selection.constants import Constants
from aktana_ml_utils import aktana_ml_utils
from athena_reader import AthenaReader


def call_openai_api(prompt: str, csv_data: str):
    try:
        # Combine prompt with CSV data
        full_prompt = f"{prompt}\n\nHere is the CSV Data:\n{csv_data}"

        # Make API call
        response = openai.ChatCompletion.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "You are a skilled Healthcare Marketing Analyst. Your job is to read promotional emails written for doctors and classify the messaging strategy used in each one."},
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.1  # Low temperature for consistent results
        )

        return response.choices[0].message.content, getattr(response, 'usage', None)

    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        raise e

def initialize_globals():
    short_opt = "hc:e:a:r:ec:o:l:d"
    long_opt = ["customer=", "env=", "app=", "region=", "ecosystem=", "json-output=", "local=", "use-debug=",
                "scenario=", "loglevel=", "autocreate=", "runMode=", "postProcMode=", "journey=",
                "useSCC=", "rundate=", "startdate=", "enddate=", "user=", "incrementalRun="]
    cmd_argv = sys.argv

    ak_ml_utils = aktana_ml_utils()
    cmdline_params, metadata_params = ak_ml_utils.initialize(cmd_argv, short_opt, long_opt, Constants.TEST_MODE)
    params = ak_ml_utils.get_params_json()
    params = json.loads(params)
    
    Constants.ADL_S3_PATH = params.get('adl-adlS3Location')
    Constants.ADL_S3_PATH = Constants.ADL_S3_PATH.replace('s3:', 's3a:')
    Constants.S3_BASE_PATH = Constants.ADL_S3_PATH.split('adl/')[0]
    Constants.TARGET_S3_PATH = Constants.S3_BASE_PATH + 'content_selection/'

    Constants.rds_hostname = params.get('rds-server')
    Constants.rds_port = params.get('rds-port') if params.get('rds-port') else 3306
    Constants.rds_username = params.get('rds-user')
    Constants.rds_password = params.get('rds-password')
    Constants.engine_db = params.get('rds-enginedbname')
    Constants.stage_db = params.get('rds-stagedbname')
    Constants.learning_db = params.get('rds-learningdbname')
    
    return params

def prepare_content_data() -> bool:
    
    try:
        params = initialize_globals()
        print("Step 1: Reading latest timestamp from existing output")

        reader = AthenaReader()
        try:
            reader.connect(
                aws_access_key=params.get("athena-username"),
                aws_secret_key=params.get("athena-password"),
                session_token=params.get('session_token'),
                aws_region=params.get("athena-region"),
                athena_staging_bucket=params.get("athena-s3bucket"),
                athena_staging_dir=params.get("athena-stagedir"),
                schema=params.get("athena-schema")
            )
        except Exception as e:
            print(f"Failed to connect to Athena: {str(e)}")
            return False

        latest_ts = None
        try:
            df_latest = reader.query("""
                select max(createdat) as max_ts
                from ctag_content_feature
            """)
            if df_latest is not None and 'max_ts' in df_latest.columns and pd.notnull(df_latest.loc[0, 'max_ts']):
                latest_ts = pd.to_datetime(df_latest.loc[0, 'max_ts'])
                print(f"Latest timestamp from ctag_content_feature: {latest_ts}")
            else:
                print("ctag_content_feature exists but has no timestamps; treating as full load.")
        except Exception as e:
            print(f"Could not read latest timestamp (table may not exist). Proceeding with full load. Details: {str(e)}")

        # Step 2: Fetch scraped content after the latest timestamp
        print("Step 2: Fetching scraped content from Athena")
        try:
            if latest_ts is not None:
                ts_str = pd.to_datetime(latest_ts).strftime('%Y-%m-%d %H:%M:%S')
                scraped_query = f"""
                    select cms_messageuid, content 
                    from ctag_scraped_content
                    where scrapedat > timestamp '{ts_str}'
                """
            else:
                scraped_query = """
                    select cms_messageuid, content 
                    from ctag_scraped_content
                """
            scraped_df = reader.query(query=scraped_query)
        except Exception as e:
            print(f"Failed to read ctag_scraped_content from Athena: {str(e)}")
            return False

        if scraped_df is None or scraped_df.empty:
            print("No new scraped content to process. Nothing to do.")
            return True

        # Prepare for per-document processing
        # scraped_df = scraped_df.head(2)
        print("Scraped content-ids:")
        print(scraped_df['cms_messageuid'])

        # Step 3: Call OpenAI API
        print("Step 3: Calling OpenAI API to generate content features...")
        openai_prompt = '''
                You are a skilled Healthcare Marketing Analyst. Your job is to read promotional emails written for doctors and classify the messaging strategy used in each one.

                Your primary goal is to analyze and categorize promotional email content sent to doctors who are not currently prescribing a specific product. You will transform unstructured email text into structured, analyzable data.

                You will be given data in csv format containing a cms_messageuid and a content column. For each row, you must carefully analyze the text in the content column and assign a value for three distinct features: Complexity, Theme, and Type.
                
                Use the following definitions as a strict rubric to guide your classifications.

                1. Complexity: The level of scientific or clinical detail in the text.
                - Low: The content uses simple, high-level language, focusing on broad benefits or brand awareness. It avoids clinical data, statistics, or complex medical terminology. Think of it as a general introduction.

                - Medium: The content presents some specific data, such as key statistics from a study, efficacy percentages, or patient profile information. It's more detailed than "Low" but doesn't require a deep dive into study methodology.

                - High: The content is dense with clinical data, discusses study methodologies, mechanism of action (MoA), or complex patient cases. It is targeted at a specialist audience and requires careful reading to fully comprehend.

                2. Theme: The primary beneficiary of the message's main point.
                - Patient benefit: The core message focuses on improving the patient's quality of life, reducing their symptoms, or offering them a more convenient treatment option. The language is patient-centric (e.g., "Help your patients achieve...").

                - Clinical benefit: The core message centers on clinical trial results, efficacy data, safety profiles, or superiority over other treatments. The focus is on the drug's performance and statistical outcomes (e.g., "Proven to reduce X by Y%").

                - HCP benefit: The core message highlights advantages for the doctor or their practice. This could include ease of prescription, a simpler dosing schedule, patient support programs that reduce office workload, or formulary access information.

                3. Type: The format or style of the content.
                - Product Brochure: The content acts as a general, often persuasive, overview of the product. It lists key features, benefits, and approved uses in a straightforward, marketing-oriented style.

                - Fact Sheet: The content is highly structured and data-centric, often using bullet points or short, declarative sentences to present key statistics, safety information, or efficacy data in a quick, easy-to-digest format.

                - Case Study: The content is presented as a narrative about a specific, often anonymized, patient. It describes the patient's initial condition, the treatment administered, and the resulting outcome, telling a story to illustrate the product's real-world application.
                                

                **Output Format Requirements**: 
                - Your entire response must be in raw CSV format.

                - The output must contain a header row: cms_messageuid,Complexity,Theme,Type.

                - Include the original cms_messageuid for each row, followed by the three new feature columns you have generated.

                - Do not include any introductory text, explanations, summaries, or markdown code blocks (like ```) in your response. The first line of your output must be the CSV header.

                '''
        # Token usage tracking
        total_prompt_tokens = 0
        total_completion_tokens = 0
        total_tokens = 0

        # Step 4: Parse OpenAI responses and create final dataframe
        print("Step 4: Parsing OpenAI responses...")

        response_df_list = []
        for _, r in scraped_df.iterrows():
            try:
                one_row_df = pd.DataFrame([r])
                # Safe CSV formatting with quoting; embedded quotes are doubled
                one_csv = one_row_df.to_csv(index=False, quoting=csv.QUOTE_ALL)
                resp_text, usage = call_openai_api(openai_prompt, one_csv)

                # Track token usage if available
                if usage is not None:
                    # usage may be dict-like
                    pt = getattr(usage, 'prompt_tokens', None)
                    ct = getattr(usage, 'completion_tokens', None)
                    tt = getattr(usage, 'total_tokens', None)
                    if pt is None and isinstance(usage, dict):
                        pt = usage.get('prompt_tokens')
                        ct = usage.get('completion_tokens')
                        tt = usage.get('total_tokens')
                    total_prompt_tokens += pt or 0
                    total_completion_tokens += ct or 0
                    total_tokens += tt or 0
                    print(f"Token usage for {r['cms_messageuid']}: prompt={pt}, completion={ct}, total={tt}")

                # Parse the single-response CSV
                try:
                    parsed_df = pd.read_csv(StringIO(resp_text))
                    response_df_list.append(parsed_df)
                except Exception as pe:
                    print(f"Error parsing OpenAI response as CSV for {r['cms_messageuid']}: {str(pe)}")
                    print("OpenAI Response:")
                    print(resp_text)
                    continue
            except Exception as ce:
                print(f"Error processing document {r['cms_messageuid']}: {str(ce)}")
                continue

        if not response_df_list:
            print("No successful OpenAI responses to process.")
            return False

        response_df = pd.concat(response_df_list, ignore_index=True)
        print(f"Accumulated token usage: prompt={total_prompt_tokens}, completion={total_completion_tokens}, total={total_tokens}")

        # Step 5: Melt the response df
        print("Step 5: Melt the response df...")
        response_df = response_df.melt(id_vars=['cms_messageuid'], var_name='feature_name', value_name='feature_value')
        response_df.sort_values(by=['cms_messageuid', 'feature_name'], inplace=True)
        response_df['createdat'] = pd.to_datetime('now')
        
        # Step 6: Upsert final result to Athena Iceberg table using MERGE
        print("Step 6: Upserting final result to Athena Iceberg table...")
        try:
            database = params.get("athena-schema")
            table = "ctag_content_feature"
            s3_iceberg_path = (Constants.S3_BASE_PATH + 'dbt/impact/CTAG_CONTENT_FEATURE_ICEBERG/').replace('s3a:', 's3:')

            # Ensure Iceberg table exists
            boto3.setup_default_session(region_name=params.get("athena-region"))
            create_tbl_sql = f"""
                CREATE TABLE IF NOT EXISTS {database}.{table} (
                cms_messageuid STRING,
                feature_name STRING,
                feature_value STRING,
                createdat TIMESTAMP
                )
                LOCATION '{s3_iceberg_path}'
                TBLPROPERTIES (
                    'table_type'='ICEBERG',
                    'format'='parquet'
                )
            """
            try:
                print("Creating Iceberg table if not exists...")
                qid = wr.athena.start_query_execution(sql=create_tbl_sql, database=database)
                # Wait for the DDL query to complete to ensure table availability
                res = wr.athena.wait_query(query_execution_id=qid)
                print(f"Query execution result: {res}")
            except Exception as e:
                print(f"Failed to create or verify Iceberg table {database}.{table}: {str(e)}")
                return False

            # Build MERGE USING (VALUES ...) statement for upsert
            if response_df.empty:
                print("No rows to upsert.")
            else:
                # Sanitize and format VALUES tuples
                def esc(val):
                    if pd.isna(val):
                        return 'NULL'
                    val_str = str(val).replace("'", "''")
                    return f"'{val_str}'"

                values_rows = []
                for _, row in response_df.iterrows():
                    cms = esc(row['cms_messageuid'])
                    fname = esc(row['feature_name'])
                    fval = esc(row['feature_value'])
                    # createdat as timestamp literal
                    ts = pd.to_datetime(row['createdat']).strftime('%Y-%m-%d %H:%M:%S')
                    ts_lit = f"TIMESTAMP '{ts}'"
                    values_rows.append(f"({cms}, {fname}, {fval}, {ts_lit})")

                values_clause = ",\n                    ".join(values_rows)
                merge_sql = f"""
                    MERGE INTO {database}.{table} AS t
                    USING (
                        VALUES
                            {values_clause}
                    ) AS s(cms_messageuid, feature_name, feature_value, createdat)
                    ON t.cms_messageuid = s.cms_messageuid AND t.feature_name = s.feature_name
                    WHEN MATCHED THEN UPDATE SET
                        feature_value = s.feature_value,
                        createdat = s.createdat
                    WHEN NOT MATCHED THEN INSERT (cms_messageuid, feature_name, feature_value, createdat)
                    VALUES (s.cms_messageuid, s.feature_name, s.feature_value, s.createdat)
                """
                try:
                    print("Upserting data into Iceberg table...")
                    qid = wr.athena.start_query_execution(sql=merge_sql, database=database)
                    res = wr.athena.wait_query(query_execution_id=qid)
                    print(f"Query execution result: {res}")
                except Exception as e:
                    print(f"Failed to execute MERGE into {database}.{table}: {str(e)}")
                    return False

            print("Successfully upserted data into ctag_content_feature (Iceberg) table.")
        except Exception as e:
            print(f"Failed to upsert data via Athena MERGE: {str(e)}")
            return False
        print(f"Final dataset contains {len(response_df)} rows and {len(response_df.columns)} columns.")

        return True

    except Exception as e:
        print(f"❌ Error in prepare_content_data: {str(e)}")
        return False


if __name__ == "__main__":
    
    success = prepare_content_data()

    if success:
        print("Content data preparation completed successfully!")
    else:
        print("Content data preparation failed!")
    exit(0 if success else 1)