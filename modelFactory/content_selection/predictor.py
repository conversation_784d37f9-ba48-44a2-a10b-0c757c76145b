import pandas as pd
import numpy as np

from abstract_model_factory.abstract_predictor import AbstractPredictor
from content_selection.data import Data
from content_selection.constants import Constants
from content_selection.data_access_layer import DataAccessLayer


class Predictor(AbstractPredictor):

    def qa_module(self):
        pass
        
    def predict_using_xgb_model(self):
        data = Data.get_instance()
        model = data.get_model("xgb_model")
        prediction_data = data.get_dataframe("prediction_df")
        prediction_data.sort_values(by=['accountId', 'contentTopicName', 'fragmentId'], inplace=True)

        if model is None:
            print("Error: No model provided for prediction")
            return prediction_data

        try:
            # Get feature names from the model
            feature_names = model.get_booster().feature_names
            print(f"Model expects features: {feature_names}")

            # Check if all required features are present
            missing_features = [f for f in feature_names if f not in prediction_data.columns]
            if missing_features:
                print(f"Warning: Missing features for prediction: {missing_features}")
                # Fill missing features with 0 or handle appropriately
                for feature in missing_features:
                    prediction_data[feature] = 0

            # Select only the features the model was trained on
            feature_data = prediction_data[feature_names]

            # Generate predictions
            print(f"Generating predictions for {len(feature_data)} records...")
            probabilities = model.predict_proba(feature_data)[:, 1]  # Get positive class probabilities

            # Add predictions to the dataframe
            prediction_data = prediction_data.copy()
            prediction_data['probability'] = probabilities

            print(f"Predictions generated successfully. Mean probability: {probabilities.mean():.4f}")

            return prediction_data

        except Exception as e:
            print(f"Error during prediction: {str(e)}")
            return prediction_data


    def execute(self):
        if Constants.EXECUTION_MODE:
            print("Executing Predictor")

            data = Data.get_instance()
            if data.get_model("xgb_model"):
                print("Model found for prediction")
            else:
                model_s3_path = DataAccessLayer.get_xgb_model_s3_path()
                model = DataAccessLayer.read_xgb_model_from_s3(model_s3_path)
                print(f"XGBoost model loaded from S3: {model_s3_path}")
                data.set_model("xgb_model", model)

            # Generate predictions using the model
            prediction_results = self.predict_using_xgb_model()

            # Keep only relevant columns
            final_results = prediction_results[['runRepDateSuggestionDetailId', 'accountId', 'contentTopicName', 'fragmentId', 'probability']]
            final_results['cmsDocumentId'] = final_results['fragmentId'].str.extract(r'^(.*)-[^-]+$')
            final_results['fragmentId'] = final_results['fragmentId'].str.extract(r'-(\d+)$').astype(int)
            final_results = final_results[['runRepDateSuggestionDetailId', 'accountId', 'contentTopicName', 'cmsDocumentId', 'fragmentId', 'probability']]

            # Store the results back in the data instance
            data = Data.get_instance()
            data.set_dataframe("prediction_results", final_results)

            print("Prediction execution completed successfully")
            print(f"Full results stored with columns: {list(final_results.columns)}")

            # Display summary statistics
            print(f"\nPrediction summary:")
            print(f"  - Total account-content combinations: {len(final_results)}")
            print(f"  - Mean probability: {final_results['probability'].mean():.4f}")
            print(f"  - Min probability: {final_results['probability'].min():.4f}")
            print(f"  - Max probability: {final_results['probability'].max():.4f}")

            # pd.set_option('display.max_columns', None)
            print(final_results)

            print("Finished executing Predictor")
        else:
            print("EXECUTION_MODE IS OFF: Skipping prediction")

